import { Authenticator } from '@aws-amplify/ui-react-native';
import { Nunito_400Regular, Nunito_700Bold, useFonts } from '@expo-google-fonts/nunito';
import * as Sentry from '@sentry/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Amplify } from 'aws-amplify';
import { isRunningInExpoGo } from 'expo';
import { Slot, useNavigationContainerRef, usePathname } from 'expo-router';
import React, { StrictMode, useEffect } from 'react';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-get-random-values';
import 'react-native-reanimated';
import styled from 'styled-components/native';
import awsExports from '../src/aws-exports';
import { AuthProvider } from '../src/context/AuthContext';
import { ThemeProvider } from '../src/theme/ThemeContext';
import { logger } from '../src/utils/logger';
import Callback from './callback';

// Construct a new integration instance. This is needed to communicate between the integration and React
const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: !isRunningInExpoGo(),
});

if (!__DEV__) {
  Sentry.init({
    dsn: 'https://<EMAIL>/4509403176108112',

    // Adds more context data to events (IP address, cookies, user, etc.)
    // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
    sendDefaultPii: true,

    debug: false, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
    tracesSampleRate: 1.0, // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing. Adjusting this value in production.

    // Configure Session Replay
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1,
    integrations: [
      Sentry.mobileReplayIntegration(),
      Sentry.feedbackIntegration(),
      navigationIntegration,
    ],
    enableNativeFramesTracking: !isRunningInExpoGo(), // Tracks slow and frozen frames in the application

    // uncomment the line below to enable Spotlight (https://spotlightjs.com)
    // spotlight: __DEV__,
  });
}
Amplify.configure(awsExports);

const StyledGestureHandlerRootView = styled(GestureHandlerRootView)`
  flex: 1;
`;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // Data is considered fresh for 5 minutes
      gcTime: 1000 * 60 * 30, // Cache is kept for 30 minutes
    },
  },
});

function RootLayout() {
  useFonts({
    Nunito: Nunito_400Regular,
    NunitoBold: Nunito_700Bold,
  });
  // Capture the NavigationContainer ref and register it with the integration.
  const ref = useNavigationContainerRef();

  useEffect(() => {
    if (ref?.current) {
      navigationIntegration.registerNavigationContainer(ref);
    }
  }, [ref]);
  const pathname = usePathname();
  logger.log('pathname:', pathname);

  return (
    <StrictMode>
      <StyledGestureHandlerRootView>
        <ThemeProvider>
          <QueryClientProvider client={queryClient}>
            <Authenticator.Provider>
              <AuthProvider>
                <Callback />
                <Slot key="Root Slot" />
              </AuthProvider>
            </Authenticator.Provider>
          </QueryClientProvider>
        </ThemeProvider>
      </StyledGestureHandlerRootView>
    </StrictMode>
  );
}

export default Sentry.wrap(RootLayout);
