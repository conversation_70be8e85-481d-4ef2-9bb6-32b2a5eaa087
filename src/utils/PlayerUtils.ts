import { calculateBestPosition as calculatePlayerBestPosition, CorePlayer } from '../models/player';
import { logger } from './logger';

export const STAMINA_SCALAR = 5.8;

// Calculate best position for any player type
export function calculatePlayerPosition(player: CorePlayer): string[] {
  return calculatePlayerBestPosition(player); // Now we can use the same function for both types
}

// Format player value to display format
export function formatPlayerValue(value: number): string {
  if (value < 1000000) {
    return `${(value / 1000).toFixed(0)}K`;
  }
  return `${(value / 1000000).toFixed(1)}M`;
}

export function calculateCurrentEnergy(stamina: number, energy: number, lastMatchPlayed: number) {
  const hoursSinceLastMatch = (Date.now() - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursSinceLastMatch));
}

export function calculateEnergyByNextMatch(
  stamina: number,
  energy: number,
  lastMatchPlayed: number,
  nextMatchTime: number
) {
  if (nextMatchTime === 0) {
    return 100;
  }
  const hoursBetweenMatches = (nextMatchTime - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursBetweenMatches));
}

// Calculate star rating based on comparison to team average
export function calculateStarRating(
  player: CorePlayer,
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker',
  teamAverages?: Record<string, number>
) {
  if (!teamAverages || Object.keys(teamAverages).length === 0) {
    return 0; // No rating if no team averages available
  }

  // Get the attribute keys for the current position filter
  const attributeKeys: string[] = [];

  if (positionFilter === 'Goalkeeper') {
    attributeKeys.push('reflexes', 'positioning', 'shotStopping');
  } else if (positionFilter === 'Defender') {
    attributeKeys.push('tackling', 'marking', 'heading');
  } else if (positionFilter === 'Midfielder') {
    attributeKeys.push('passing', 'vision', 'ballControl');
  } else if (positionFilter === 'Attacker') {
    attributeKeys.push('finishing', 'pace', 'crossing');
  } else {
    // For 'All', compare all attributes
    attributeKeys.push(
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'passing',
      'vision',
      'ballControl',
      'finishing',
      'pace',
      'crossing'
    );
  }

  // Calculate the average difference between player attributes and team average
  let totalDifference = 0;
  let count = 0;

  attributeKeys.forEach((key) => {
    if (teamAverages[key] && player.attributes[key as keyof typeof player.attributes]) {
      const playerValue = player.attributes[key as keyof typeof player.attributes] as number;
      const teamAverage = teamAverages[key];
      totalDifference += playerValue - teamAverage;
      count++;
    }
  });

  if (count === 0) return 0;

  // Calculate average difference and convert to a 0-10 scale
  // If player equals team average, rating is 5
  // Max attribute is 20, so max difference is 20
  // Scale: -10 to +10 difference maps to 0-10 rating
  const avgDifference = totalDifference / count;
  const rating = 5 + avgDifference / 4; // 4 points difference = 1-star difference

  // Clamp between 0 and 10
  return Math.max(0, Math.min(10, rating));
}
