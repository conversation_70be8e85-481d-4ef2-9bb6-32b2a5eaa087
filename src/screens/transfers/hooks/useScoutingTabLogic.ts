import { useEffect, useState } from 'react';
import { ScoutedPlayer } from '../../../hooks/useQueries';
import { logger } from '../../../utils/logger';

export type PositionFilter = 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';

interface UseScoutingTabLogicProps {
  scoutedPlayersData?: {
    scoutedPlayers: ScoutedPlayer[];
    pagination?: {
      hasMore: boolean;
      nextToken?: string;
    };
  };
  isLoadingMore: boolean;
  nextToken?: string;
}

export const useScoutingTabLogic = ({
  scoutedPlayersData,
  isLoadingMore,
  nextToken,
}: UseScoutingTabLogicProps) => {
  const [scoutedPlayers, setScoutedPlayers] = useState<ScoutedPlayer[]>([]);
  const [selectedPlayer, setSelectedPlayer] = useState<ScoutedPlayer | null>(null);
  const [positionFilter, setPositionFilter] = useState<PositionFilter>('All');
  const [showPositionDropdown, setShowPositionDropdown] = useState(false);
  const [reachedEnd, setReachedEnd] = useState(false);

  // Update scoutedPlayers when data is fetched
  useEffect(() => {
    if (scoutedPlayersData) {
      logger.log('Scouted players data received:', scoutedPlayersData);

      // Check if we've reached the end of the list
      if (isLoadingMore && scoutedPlayersData.scoutedPlayers.length === 0) {
        logger.log('Received empty page, reached end of list');
        setReachedEnd(true);
        return;
      }

      if (nextToken && isLoadingMore) {
        // Append new players to existing list with duplicate filtering
        setScoutedPlayers((prev) => {
          if (!Array.isArray(prev)) {
            logger.warn('Previous players is not an array:', prev);
            return scoutedPlayersData.scoutedPlayers;
          }

          // Filter out duplicates before combining
          const existingPlayerIds = new Set(prev.map((player) => player.playerId));
          const uniqueNewPlayers = scoutedPlayersData.scoutedPlayers.filter(
            (player) => !existingPlayerIds.has(player.playerId)
          );

          logger.log('Existing players:', prev.length);
          logger.log('New unique players:', uniqueNewPlayers.length);

          // If we got no new unique players, we've reached the end
          if (uniqueNewPlayers.length === 0 && isLoadingMore) {
            logger.log('No new unique players, reached end of list');
            setReachedEnd(true);
          }

          // Combine previous and unique new players
          const combinedPlayers = [...prev, ...uniqueNewPlayers];
          logger.log('Combined players count:', combinedPlayers.length);
          return combinedPlayers;
        });
      } else {
        // Replace the list with new data for initial load
        setScoutedPlayers(scoutedPlayersData.scoutedPlayers);
        // Reset reached end flag on initial load
        setReachedEnd(false);
      }
    }
  }, [scoutedPlayersData, isLoadingMore, nextToken]);

  const togglePositionDropdown = () => {
    setShowPositionDropdown(!showPositionDropdown);
  };

  const handlePositionSelect = (position: PositionFilter) => {
    setPositionFilter(position);
    setShowPositionDropdown(false);
  };

  const handlePlayerSelect = (player: ScoutedPlayer) => {
    setSelectedPlayer(player);
  };

  const clearSelectedPlayer = () => {
    setSelectedPlayer(null);
  };

  return {
    scoutedPlayers,
    selectedPlayer,
    positionFilter,
    showPositionDropdown,
    reachedEnd,
    togglePositionDropdown,
    handlePositionSelect,
    handlePlayerSelect,
    clearSelectedPlayer,
  };
};
