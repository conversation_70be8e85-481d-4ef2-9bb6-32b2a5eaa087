import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useManager } from '../../../context/ManagerContext';
import { useDataCache } from '../../../context/DataCacheContext';
import { ActiveTransfer } from '../../../hooks/useMyBidsPlayers';
import { logger } from '../../../utils/logger';

interface TransferResponse {
  newBalance?: number;
  status: number;
}

export const useTransferActions = () => {
  const { manager } = useManager();
  const queryClient = useQueryClient();
  const { updateManager } = useDataCache();
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showError, setShowError] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<ActiveTransfer | null>(null);
  const [negotiatingTransfer, setNegotiatingTransfer] = useState<ActiveTransfer | null>(null);
  const [isNegotiateModalVisible, setIsNegotiateModalVisible] = useState(false);
  const [negotiateAmount, setNegotiateAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAcceptCounterOffer = async (transfer: ActiveTransfer) => {
    try {
      setIsLoading(true);
      const response = await callApi<TransferResponse>(`/transfer/accept`, {
        method: 'POST',
        body: JSON.stringify({
          transferRequestId: transfer.id,
        }),
      });

      // Update cache with new balance if provided in response
      if (response?.newBalance !== undefined && manager?.team) {
        updateManager({
          team: {
            ...manager.team,
            balance: response.newBalance
          }
        });
      }

      // Refresh the active transfers data to show the updated offer
      await queryClient.invalidateQueries({
        queryKey: ['myActiveTransfers', manager?.gameworldId],
      });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error accepting counter offer:', err);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNegotiate = (transfer: ActiveTransfer) => {
    // Prefill with the user's last offer amount
    setNegotiateAmount(transfer.value.toString());
    setNegotiatingTransfer(transfer);
    setIsNegotiateModalVisible(true);
  };

  const handleSubmitNegotiation = async () => {
    if (!negotiateAmount || isNaN(Number(negotiateAmount)) || !negotiatingTransfer) {
      setShowError(true);
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await callApi<TransferResponse>(`/transfer/offer`, {
        method: 'POST',
        body: JSON.stringify({
          player: negotiatingTransfer.player.playerId,
          offer: Number(negotiateAmount),
          theirTeam: negotiatingTransfer.seller.teamId,
          myTeam: manager?.team?.teamId,
        }),
      });

      // Update cache with new balance if provided in response
      if (response?.newBalance !== undefined && manager?.team) {
        updateManager({
          team: {
            ...manager.team,
            balance: response.newBalance
          }
        });
      }

      setIsNegotiateModalVisible(false);
      setNegotiatingTransfer(null);
      setNegotiateAmount('');

      // Refresh the active transfers data to show the updated offer
      queryClient.invalidateQueries({ queryKey: ['myActiveTransfers', manager?.gameworldId] });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error submitting negotiation:', err);
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelTransfer = async (transfer: ActiveTransfer) => {
    try {
      setIsLoading(true);
      const response = await callApi<TransferResponse>('/transfer/cancel', {
        method: 'POST',
        body: JSON.stringify({
          transferRequestId: transfer.id,
        }),
      });

      // Update cache with new balance if provided in response
      if (response?.newBalance !== undefined && manager?.team) {
        updateManager({
          team: {
            ...manager.team,
            balance: response.newBalance
          }
        });
      }

      // Refresh the active transfers data to remove the cancelled transfer
      await queryClient.invalidateQueries({
        queryKey: ['myActiveTransfers', manager?.gameworldId],
      });

      setShowConfirmation(true);
    } catch (err) {
      logger.error('Error cancelling transfer:', err);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTransferSelect = (transfer: ActiveTransfer) => {
    setSelectedTransfer(transfer);
  };

  const clearSelectedTransfer = () => {
    setSelectedTransfer(null);
  };

  const closeNegotiateModal = () => {
    setIsNegotiateModalVisible(false);
    setNegotiateAmount('');
    setNegotiatingTransfer(null);
  };

  return {
    isLoading,
    showConfirmation,
    showError,
    selectedTransfer,
    negotiatingTransfer,
    isNegotiateModalVisible,
    negotiateAmount,
    isSubmitting,
    setShowConfirmation,
    setShowError,
    setNegotiateAmount,
    handleAcceptCounterOffer,
    handleNegotiate,
    handleSubmitNegotiation,
    handleCancelTransfer,
    handleTransferSelect,
    clearSelectedTransfer,
    closeNegotiateModal,
  };
};
