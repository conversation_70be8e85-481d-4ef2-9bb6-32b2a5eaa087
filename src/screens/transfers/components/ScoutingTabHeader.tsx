import React from 'react';
import { Image, Platform, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { ButtonContainer, ButtonText } from '../../../components/TransferSharedStyles';
import { Text } from '../../../components/Text';
import { Manager } from '../../../models/manager';
import { PositionFilter } from '../hooks/useScoutingTabLogic';

interface StyledProps {
  theme: DefaultTheme;
}

const ScoutButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  padding: 12px 24px;
  border-radius: 8px;
  flex: 1;
  align-items: center;
  justify-content: center;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const FilterContainer = styled.View`
  margin-bottom: 16px;
  z-index: 999;
  position: relative;
`;

const DropdownContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 0 12px;
  position: relative;
  z-index: 999;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const DropdownButton = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
`;

const DropdownText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'Nunito Bold';
  font-size: 16px;
`;

const DropdownOptions = styled.View<StyledProps>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  margin-top: 4px;
  z-index: 999;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

interface OptionItemProps extends StyledProps {
  isSelected?: boolean;
}

const OptionItem = styled.TouchableOpacity<OptionItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

interface OptionTextProps extends StyledProps {
  isSelected?: boolean;
}

const OptionText = styled(Text)<OptionTextProps>`
  color: ${(props) =>
    props.isSelected ? props.theme.colors.primary : props.theme.colors.text.primary};
  font-family: ${(props) => (props.isSelected ? 'NunitoBold' : 'Nunito')};
  font-size: 16px;
`;

const TokenCountContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;
  gap: 16px;
`;

const TokenTypeContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
`;

const TokenIcon = styled(Image)`
  width: 24px;
  height: 24px;
  margin-right: 4px;
`;

const TokenCountText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  font-size: 16px;
`;

interface ScoutingTabHeaderProps {
  manager?: Manager | null;
  positionFilter: PositionFilter;
  showPositionDropdown: boolean;
  onScoutLeague: () => void;
  onScoutTeam: () => void;
  onTogglePositionDropdown: () => void;
  onPositionSelect: (position: PositionFilter) => void;
  dropdownRef: React.RefObject<any>;
}

export const ScoutingTabHeader: React.FC<ScoutingTabHeaderProps> = ({
  manager,
  positionFilter,
  showPositionDropdown,
  onScoutLeague,
  onScoutTeam,
  onTogglePositionDropdown,
  onPositionSelect,
  dropdownRef,
}) => {
  const positionOptions: PositionFilter[] = ['All', 'Goalkeeper', 'Defender', 'Midfielder', 'Attacker'];

  return (
    <>
      <TokenCountContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../../assets/scoutToken.png')} />
          <TokenCountText>{manager?.scoutTokens ?? 0}</TokenCountText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../../assets/superScoutToken.png')} />
          <TokenCountText>{manager?.superScoutTokens ?? 0}</TokenCountText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../../assets/quid.svg')} />
          <TokenCountText>{manager?.team?.balance?.toLocaleString() ?? '0'}</TokenCountText>
        </TokenTypeContainer>
      </TokenCountContainer>

      <ButtonContainer>
        <ScoutButton onPress={onScoutLeague}>
          <ButtonText>Scout League</ButtonText>
        </ScoutButton>
        <ScoutButton onPress={onScoutTeam}>
          <ButtonText>Scout Team</ButtonText>
        </ScoutButton>
      </ButtonContainer>

      <FilterContainer>
        <View ref={dropdownRef}>
          <DropdownContainer>
            <DropdownButton onPress={onTogglePositionDropdown}>
              <DropdownText>Show Attributes: {positionFilter}</DropdownText>
              <DropdownText>{showPositionDropdown ? '▲' : '▼'}</DropdownText>
            </DropdownButton>
            {showPositionDropdown && (
              <DropdownOptions>
                {positionOptions.map((position) => (
                  <OptionItem
                    key={position}
                    onPress={() => onPositionSelect(position)}
                    isSelected={positionFilter === position}
                  >
                    <OptionText isSelected={positionFilter === position}>{position}</OptionText>
                  </OptionItem>
                ))}
              </DropdownOptions>
            )}
          </DropdownContainer>
        </View>
      </FilterContainer>
    </>
  );
};
