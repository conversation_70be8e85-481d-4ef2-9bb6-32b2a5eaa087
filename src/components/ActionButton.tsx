import styled from 'styled-components/native';
import { Text } from './Text';

export const ActionButton = styled.TouchableOpacity<{
  variant?: 'primary' | 'secondary' | 'danger';
}>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.variant) {
      case 'danger':
        return '#e3172a';
      case 'secondary':
        return '#888';
      default:
        return props.theme.colors.primary;
    }
  }};
`;
export const ActionButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 14px;
  text-align: center;
`;
