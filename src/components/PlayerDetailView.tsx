import React, { useEffect } from 'react';
import { Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useManager } from '../context/ManagerContext';
import { usePlayerUtils } from '../context/PlayerContext';
import { ActiveTransfer } from '../hooks/useMyBidsPlayers';
import { CorePlayer, Player, TransferListPlayer } from '../models/player';
import { Team } from '../models/team';
import { CrossPlatformAlert } from './CrossPlatformAlert';
import { AttributeTable } from './PlayerDetail/AttributeTable';
import { MagicSpongeModal } from './PlayerDetail/MagicSpongeModal';
import { PlayerHeader } from './PlayerDetail/PlayerHeader';
import { PlayerInfo } from './PlayerDetail/PlayerInfo';
import { PlayerStatus } from './PlayerDetail/PlayerStatus';
import { ReleasePlayerModal } from './PlayerDetail/ReleasePlayerModal';
import { TransferModal } from './PlayerDetail/TransferModal';
import { TransferSection } from './PlayerDetail/TransferSection';
import { usePlayerStatus } from './PlayerDetail/hooks/usePlayerStatus';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerDetailViewProps {
  player: CorePlayer;
  team?: Team;
  onClose: () => void;
  onNewTransfer?: (transfer: ActiveTransfer) => void;
}

const DetailView = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 16px;
  width: 100%;
  height: 100%;
`;

const ScrollContainer = styled.ScrollView`
  flex: 1;
`;

const PlayerDetailView: React.FC<PlayerDetailViewProps> = ({
  player,
  team,
  onClose,
  onNewTransfer,
}) => {
  const { manager, team: userTeam } = useManager();

  // Custom hooks
  const playerStatus = usePlayerStatus(player, userTeam || undefined, team);
  const { playerActionsState, playerActions } = usePlayerUtils();

  const [isModalVisible, setModalVisible] = React.useState(false);

  // Group attributes by category
  const goalkeepingAttributes = [
    { name: 'Reflexes', value: player.attributes.reflexes },
    { name: 'Positioning', value: player.attributes.positioning },
    { name: 'Shot Stopping', value: player.attributes.shotStopping },
  ];

  const defendingAttributes = [
    { name: 'Tackling', value: player.attributes.tackling },
    { name: 'Marking', value: player.attributes.marking },
    { name: 'Heading', value: player.attributes.heading },
  ];

  const midfieldingAttributes = [
    { name: 'Passing', value: player.attributes.passing },
    { name: 'Vision', value: player.attributes.vision },
    { name: 'Ball Control', value: player.attributes.ballControl },
  ];

  const attackingAttributes = [
    { name: 'Finishing', value: player.attributes.finishing },
    { name: 'Pace', value: player.attributes.pace },
    { name: 'Crossing', value: player.attributes.crossing },
  ];

  useEffect(() => {
    if (player) {
      setModalVisible(true);
    }
  }, [player]);

  return (
    <>
      <Modal
        visible={isModalVisible}
        transparent={false}
        animationType="slide"
        onRequestClose={onClose}
      >
        <DetailView>
          <PlayerHeader player={player} onClose={onClose} />

          <MagicSpongeModal
            player={player as Player}
            isInjured={playerStatus.isInjured}
            spongesAvailable={manager?.magicSponges ?? 0}
          />

          <ScrollContainer showsVerticalScrollIndicator={false}>
            <PlayerInfo player={player} team={team} playerStatus={playerStatus} />

            <TransferSection
              player={player as TransferListPlayer}
              playerStatus={playerStatus}
              isPlayerInUserTeam={playerStatus.isPlayerInUserTeam}
              isAuctionPlayer={playerStatus.isAuctionPlayer}
              maxBid={playerActionsState.maxBid}
              isHighestBidder={playerActionsState.isHighestBidder}
              onNewTransfer={onNewTransfer}
            />

            {playerStatus.isPlayerInUserTeam && (
              <PlayerStatus
                player={player as Player}
                isInjured={playerStatus.isInjured}
                isSuspended={playerStatus.isSuspended}
              />
            )}

            <AttributeTable title="Goalkeeping" attributes={goalkeepingAttributes} />
            <AttributeTable title="Defending" attributes={defendingAttributes} />
            <AttributeTable title="Midfielding" attributes={midfieldingAttributes} />
            <AttributeTable title="Attacking" attributes={attackingAttributes} />
          </ScrollContainer>

          <TransferModal
            player={player as TransferListPlayer}
            playerStatus={playerStatus}
            isAuctionPlayer={playerStatus.isAuctionPlayer}
            formattedValue={playerStatus.formattedValue}
            onClose={() => playerActions.setIsOfferModalVisible(false)}
            onNewTransfer={onNewTransfer}
          />

          {playerStatus.isPlayerInUserTeam && <ReleasePlayerModal player={player as Player} />}

          <CrossPlatformAlert
            visible={playerActionsState.showAlert}
            title={playerActionsState.alertMessage.title}
            message={playerActionsState.alertMessage.message}
            buttons={[
              {
                text: 'OK',
                onPress: () => {
                  playerActions.setShowAlert(false);
                  onClose();
                },
              },
            ]}
            onDismiss={() => playerActions.setShowAlert(false)}
          />
        </DetailView>
      </Modal>
    </>
  );
};

export default PlayerDetailView;
