import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, Image, TouchableOpacity, View } from 'react-native';
import { GestureDetector } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { TeamInputMethod } from '../../context/TeamInputMethodContext';
import { Player } from '../../models/player';
import { calculatePlayerPosition } from '../../utils/PlayerUtils';
import { usePlayerStatus } from '../PlayerDetail/hooks/usePlayerStatus';
import {
  AttributeGroup,
  AttributeLabel,
  AttributeValue,
  AttributesContainer,
  Card,
  CardContent,
  CardHeader,
  DetailButton,
  DraggerContainer,
  DraggerLine,
  EnergyContainer,
  EnergyText,
  PlayerName,
  PlayerPosition,
  PlayerValue,
  StatusIconContainer,
} from './PlayerRowStyles';
import { DragHandle } from './SharedComponents';

// Swap icon component for tap mode
const SwapIcon: React.FC<{ isSelected: boolean; onPress: () => void }> = ({
  isSelected,
  onPress,
}) => (
  <TouchableOpacity
    onPress={onPress}
    style={{ width: 60, alignItems: 'center', justifyContent: 'center' }}
  >
    <MaterialIcons name="swap-vert" size={24} color={isSelected ? '#2196F3' : '#ffffff'} />
  </TouchableOpacity>
);

interface TeamPlayerRowProps {
  player: Player;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  onSelect?: (player: Player) => void;
  dragGesture?: any;
  inputMethod?: TeamInputMethod;
  isSelectedForSwap?: boolean;
  onPlayerTap?: (playerId: string) => void;
}

const TeamPlayerRow: React.FC<TeamPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  onSelect,
  dragGesture,
  inputMethod = 'drag',
  isSelectedForSwap = false,
  onPlayerTap,
}) => {
  const positions = calculatePlayerPosition(player);
  const { formattedValue, currentEnergy, isInjured, isSuspended, isRetiring } =
    usePlayerStatus(player);

  // Animation for selection in tap mode
  const scale = useSharedValue(1);
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  // Update animation when selection state changes
  React.useEffect(() => {
    if (inputMethod === 'tap' && isSelectedForSwap) {
      scale.value = withSpring(0.95);
    } else {
      scale.value = withSpring(1);
    }
  }, [isSelectedForSwap, inputMethod, scale]);

  const handlePress = () => {
    if (inputMethod === 'tap' && onPlayerTap) {
      onPlayerTap(player.playerId);
    } else if (onSelect) {
      onSelect(player);
    }
  };

  const handleSwapIconPress = () => {
    if (inputMethod === 'tap' && onPlayerTap) {
      onPlayerTap(player.playerId);
    }
  };

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check if we're on mobile and update on window resize
  const [isMobile, setIsMobile] = React.useState(Dimensions.get('window').width < 768);

  React.useEffect(() => {
    // Function to update the isMobile state based on window width
    const updateLayout = () => {
      const width = Dimensions.get('window').width;
      setIsMobile(width < 768);
    };

    // Add event listener for window resize
    const dimensionsHandler = Dimensions.addEventListener('change', updateLayout);

    // Clean up event listener on component unmount
    return () => {
      dimensionsHandler.remove();
    };
  }, []);

  // Determine background color based on injury and suspension status
  let cardBackgroundColor = backgroundColor;
  if (isInjured || isSuspended) {
    cardBackgroundColor = '#e3172a'; // Light red for injured or suspended
  }

  return (
    <Animated.View style={animatedStyle}>
      <Card
        isSelected={isSelected || isSelectedForSwap}
        backgroundColor={cardBackgroundColor}
        isActive={isActive}
      >
        {/* Render based on input method */}
        {inputMethod === 'drag' && dragGesture ? (
          <GestureDetector gesture={dragGesture}>
            <CardContent>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <DragHandle />
                <View style={{ flex: 1 }}>
                  <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
                    {!showAttributes && <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>}
                    {(isInjured || isSuspended) && (
                      <StatusIconContainer>
                        {isInjured && (
                          <Image
                            source={require('../../../assets/injury.png')}
                            style={{ width: 16, height: 16 }}
                            resizeMode="contain"
                          />
                        )}
                        {isSuspended && (
                          <View
                            style={{
                              position: 'relative',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Image
                              source={require('../../../assets/redcard.png')}
                              style={{ width: 16, height: 16 }}
                              resizeMode="contain"
                            />
                          </View>
                        )}
                        {isRetiring && (
                          <View
                            style={{
                              position: 'relative',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Image
                              source={require('../../../assets/retiring.png')}
                              style={{ width: 16, height: 16 }}
                              resizeMode="contain"
                            />
                          </View>
                        )}
                      </StatusIconContainer>
                    )}
                    <PlayerName
                      isMobile={isMobile}
                      isUnavailable={isInjured || isSuspended}
                      style={{ flex: showAttributes ? 1 : undefined, textAlign: 'left' }}
                    >
                      {`${player.firstName} ${player.surname}`}
                    </PlayerName>
                  </CardHeader>
                  {!showAttributes && (
                    <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
                      <EnergyContainer>
                        <EnergyText>Energy: {`${currentEnergy}%`}</EnergyText>
                        <PlayerValue>{formattedValue}</PlayerValue>
                      </EnergyContainer>
                    </AttributesContainer>
                  )}
                  {showAttributes && (
                    <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
                      {attributesToShow.map((attr, index) => (
                        <AttributeGroup key={index}>
                          <AttributeLabel>{attr.label}:</AttributeLabel>
                          <AttributeValue>{attr.value}</AttributeValue>
                        </AttributeGroup>
                      ))}
                    </AttributesContainer>
                  )}
                </View>
              </View>
            </CardContent>
          </GestureDetector>
        ) : (
          <TouchableOpacity onPress={handlePress} style={{ flex: 1 }}>
            <CardContent>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {inputMethod === 'tap' ? (
                  <SwapIcon isSelected={isSelectedForSwap} onPress={handleSwapIconPress} />
                ) : (
                  <DraggerContainer>
                    <DraggerLine />
                    <DraggerLine />
                    <DraggerLine style={{ marginBottom: 0 }} />
                  </DraggerContainer>
                )}
                <View style={{ flex: 1 }}>
                  <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
                    {!showAttributes && <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>}
                    {(isInjured || isSuspended) && (
                      <StatusIconContainer>
                        {isInjured && <MaterialIcons name="healing" size={16} color="#ffffff" />}
                        {isSuspended && (
                          <View
                            style={{
                              position: 'relative',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                          </View>
                        )}
                      </StatusIconContainer>
                    )}
                    <PlayerName
                      isMobile={isMobile}
                      isUnavailable={isInjured || isSuspended}
                      style={{ flex: showAttributes ? 1 : undefined, textAlign: 'left' }}
                    >
                      {`${player.firstName} ${player.surname}`}
                    </PlayerName>
                    {!showAttributes && (
                      <EnergyContainer>
                        <EnergyText>{`${currentEnergy}%`}</EnergyText>
                        <PlayerValue>{formattedValue}</PlayerValue>
                      </EnergyContainer>
                    )}
                  </CardHeader>
                  {showAttributes && (
                    <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
                      {attributesToShow.map((attr, index) => (
                        <AttributeGroup key={index}>
                          <AttributeLabel>{attr.label}:</AttributeLabel>
                          <AttributeValue>{attr.value}</AttributeValue>
                        </AttributeGroup>
                      ))}
                    </AttributesContainer>
                  )}
                </View>
              </View>
            </CardContent>
          </TouchableOpacity>
        )}

        {/* Detail button on the right side */}
        {onSelect && inputMethod !== 'tap' && (
          <DetailButton onPress={handlePress}>
            <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
          </DetailButton>
        )}
      </Card>
    </Animated.View>
  );
};

export default TeamPlayerRow;
