import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, Image, View } from 'react-native';
import styled from 'styled-components/native';
import { CorePlayer } from '../../models/player';
import { calculatePlayerPosition } from '../../utils/PlayerUtils';
import { usePlayerStatus } from '../PlayerDetail/hooks/usePlayerStatus';
import {
  AttributeGroup,
  AttributeLabel,
  AttributeValue,
  AttributesContainer,
  CardHeader,
  EnergyContainer,
  EnergyText,
  PlayerName,
  PlayerPosition,
  PlayerValue,
  StatusIconContainer,
} from './PlayerRowStyles';

interface PlayerInfoProps {
  player: CorePlayer;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  showImages?: boolean; // Whether to show injury/suspension images or icons
  showValue?: boolean;
}

const PlayerInfo: React.FC<PlayerInfoProps> = ({
  player,
  positionFilter = 'All',
  showImages = false,
  showValue = true,
}) => {
  const { formattedValue, currentEnergy, energyByNextMatch, isInjured, isSuspended, isRetiring } =
    usePlayerStatus(player);
  const positions = calculatePlayerPosition(player);

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check screen width to determine if we're on mobile
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  // Styled component for energy value with dynamic color logic and React Native text shadow for readability
  const EnergyValue = styled.Text<{ energy: number }>`
    color: ${({ energy }) => {
      const clamped = Math.max(0, Math.min(100, energy));
      const r = Math.round(255 * (1 - clamped / 100));
      const g = Math.round(200 * (clamped / 100));
      return `rgb(${r},${g},0)`;
    }};
    font-weight: bold;
    text-shadow-color: ${(props) => props.theme.colors.shadow};
    text-shadow-offset: 2px 2px;
    text-shadow-radius: 2px;
  `;

  return (
    <View style={{ flex: 1 }}>
      <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
        <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>
        {(isInjured || isSuspended || isRetiring) && (
          <StatusIconContainer>
            {isInjured &&
              (showImages ? (
                <Image
                  source={require('../../../assets/injury.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              ) : (
                <MaterialIcons name="healing" size={16} color="#ffffff" />
              ))}
            {isSuspended && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                {showImages ? (
                  <Image
                    source={require('../../../assets/redcard.png')}
                    style={{ width: 16, height: 16 }}
                    resizeMode="contain"
                  />
                ) : (
                  <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                )}
              </View>
            )}
            {isRetiring && showImages && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                <Image
                  source={require('../../../assets/retiring.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              </View>
            )}
          </StatusIconContainer>
        )}
        <PlayerName
          isMobile={isMobile}
          isUnavailable={isInjured || isSuspended}
          style={{ flex: showAttributes ? 1 : undefined, textAlign: 'left' }}
        >
          {`${player.firstName} ${player.surname}`}
        </PlayerName>
      </CardHeader>
      {!showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          <EnergyContainer>
            {currentEnergy && energyByNextMatch && (
              <EnergyText>
                Energy:{' '}
                {currentEnergy < 100 ? (
                  <>
                    <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
                    {' → '}
                    <EnergyValue energy={energyByNextMatch}>{`${energyByNextMatch}%`}</EnergyValue>
                  </>
                ) : (
                  <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
                )}
              </EnergyText>
            )}
            {showValue && <PlayerValue>{formattedValue}</PlayerValue>}
          </EnergyContainer>
        </AttributesContainer>
      )}
      {showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          {attributesToShow.map((attr, index) => (
            <AttributeGroup key={index}>
              <AttributeLabel>{attr.label}:</AttributeLabel>
              <AttributeValue>{Math.floor(attr.value)}</AttributeValue>
            </AttributeGroup>
          ))}
        </AttributesContainer>
      )}
    </View>
  );
};

export default PlayerInfo;
