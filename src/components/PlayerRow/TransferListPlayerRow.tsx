import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { TouchableOpacityProps } from 'react-native';
import { TransferListPlayer } from '../../models/player';
import { calculateStarRating } from '../../utils/PlayerUtils';
import PlayerInfo from './PlayerInfo';
import { Card, CardContent, CardHeader, DetailButton } from './PlayerRowStyles';
import { AuctionInfo } from './SharedComponents';
import StarRatingPill from './StarRatingPill';

interface TransferListPlayerRowProps extends TouchableOpacityProps {
  player: TransferListPlayer;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  onSelect?: (player: TransferListPlayer) => void;
  isHighestBidder?: boolean;
}

const TransferListPlayerRow: React.FC<TransferListPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  teamAverages = {},
  onSelect,
  isHighestBidder = false,
  ...props
}) => {
  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  const starRating = calculateStarRating(player, positionFilter, teamAverages);
  const fullStars = Math.floor(starRating / 2);
  const halfStar = starRating % 2 >= 0.5;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

  // Determine card background colour
  return (
    <Card isSelected={isSelected} backgroundColor={backgroundColor} isActive={isActive} {...props}>
      <StarRatingPill fullStars={fullStars} halfStar={halfStar} emptyStars={emptyStars} />
      <CardContent>
        <CardHeader>
          <PlayerInfo
            player={player}
            positionFilter={positionFilter}
            showImages={true}
            showValue={false}
          />
        </CardHeader>
      </CardContent>

      {/* Auction info on the right side */}
      <AuctionInfo player={player} isHighestBidder={isHighestBidder} />

      {/* Detail button on the right side */}
      {onSelect && (
        <DetailButton onPress={handlePress}>
          <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
        </DetailButton>
      )}
    </Card>
  );
};

export default TransferListPlayerRow;
