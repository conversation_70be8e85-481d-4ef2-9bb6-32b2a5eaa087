import React from 'react';
import styled from 'styled-components/native';
import { Player } from '../../models/player';
import { Text } from '../Text';

interface PlayerStatusProps {
  player: Player;
  isInjured: boolean;
  isSuspended: boolean;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

export const PlayerStatus: React.FC<PlayerStatusProps> = ({ player, isInjured, isSuspended }) => {
  // Format the injury date
  const formatInjuryDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (!isInjured && !isSuspended) {
    return null;
  }

  return (
    <>
      {isInjured && (
        <StatusContainer>
          <StatusText>{`Injured until ${formatInjuryDate(player.injuredUntil!)}`}</StatusText>
        </StatusContainer>
      )}

      {isSuspended && (
        <StatusContainer>
          <StatusText>{`Suspended for ${player.suspendedForGames} games`}</StatusText>
        </StatusContainer>
      )}
    </>
  );
};
