import { useMemo } from 'react';
import { CorePlayer } from '../../../models/player';
import { Team } from '../../../models/team';
import {
  calculateCurrentEnergy,
  calculateEnergyByNextMatch,
  formatPlayerValue,
} from '../../../utils/PlayerUtils';

export interface PlayerStatus {
  formattedValue: string;
  currentEnergy: number | undefined;
  energyByNextMatch?: number | undefined;
  isInjured: boolean;
  isSuspended: boolean;
  isRetiring: boolean;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
}

export const usePlayerStatus = (player: CorePlayer, userTeam?: Team, team?: Team): PlayerStatus => {
  return useMemo(() => {
    const formattedValue = formatPlayerValue(player.value);
    const now = Date.now();
    const isInjured = player.injuredUntil && player.injuredUntil > now;
    const isSuspended = !!player.suspendedForGames && player.suspendedForGames > 0;
    const isPlayerInUserTeam = userTeam && player.teamId === userTeam.teamId;
    const isAuctionPlayer = player.teamId === '';
    const isRetiring = player.retiringAtEndOfSeason;

    const currentEnergy =
      player.energy && player.lastMatchPlayed
        ? calculateCurrentEnergy(player.attributes.stamina, player.energy, player.lastMatchPlayed)
        : undefined;

    const energyByNextMatch =
      player.energy && player.lastMatchPlayed && team?.nextFixture
        ? calculateEnergyByNextMatch(
            player.attributes.stamina,
            player.energy,
            player.lastMatchPlayed,
            team.nextFixture.date
          )
        : undefined;

    return {
      formattedValue,
      currentEnergy,
      isInjured: !!isInjured,
      isSuspended,
      isRetiring: !!isRetiring,
      isPlayerInUserTeam: !!isPlayerInUserTeam,
      isAuctionPlayer,
      energyByNextMatch,
    };
  }, [player, userTeam, team]);
};
