import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { ActiveTransfer } from '../../../hooks/useMyBidsPlayers';
import { Player, TransferListPlayer } from '../../../models/player';
import { formatPlayerValue } from '../../../utils/PlayerUtils';
import { logger } from '../../../utils/logger';

import { goBack } from 'expo-router/build/global-state/routing';

export interface AlertMessage {
  title: string;
  message: string;
}

export interface PlayerActionsState {
  isOfferModalVisible: boolean;
  offerAmount: number;
  isSubmitting: boolean;
  showAlert: boolean;
  alertCloseAction: (() => void) | undefined;
  alertMessage: AlertMessage;
  maxBid: number | null;
  isHighestBidder: boolean;
  isReleaseModalVisible: boolean;
  isReleasing: boolean;
}

export interface PlayerActionsActions {
  setIsOfferModalVisible: (visible: boolean) => void;
  setOfferAmount: (amount: number) => void;
  setShowAlert: (show: boolean) => void;
  setAlertCloseAction: (action: (() => void) | undefined) => void;
  setIsReleaseModalVisible: (visible: boolean) => void;
  handleSubmitOffer: (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number,
    onNewTransfer?: (transfer: ActiveTransfer) => void
  ) => Promise<void>;
  handleReleasePlayer: (player: Player, onClose: () => void) => Promise<void>;
  prefillOfferAmount: (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => void;
}

/**
 * This should only be included by the player context!
 */
export const usePlayerActions = (): [PlayerActionsState, PlayerActionsActions] => {
  const { manager, team: userTeam, updateTeam } = useManager();
  const { updatePlayer } = useDataCache();

  const [isOfferModalVisible, setIsOfferModalVisible] = useState(false);
  const [offerAmount, setOfferAmount] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({ title: '', message: '' });
  const [alertCloseAction, setAlertCloseAction] = useState<() => void | undefined>();
  const [maxBid, setMaxBid] = useState<number | null>(null);
  const [isHighestBidder, setIsHighestBidder] = useState<boolean>(false);
  const [isReleaseModalVisible, setIsReleaseModalVisible] = useState(false);
  const [isReleasing, setIsReleasing] = useState(false);

  const showAlertMessage = (title: string, message: string) => {
    setAlertMessage({ title, message });
    setShowAlert(true);
  };

  const handleSubmitOffer = async (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number,
    onNewTransfer?: (transfer: ActiveTransfer) => void
  ) => {
    logger.debug('Submitting offer for player:', player.playerId);
    if (!offerAmount || isNaN(Number(offerAmount))) {
      showAlertMessage('Invalid Amount', 'Please enter a valid transfer amount.');
      return;
    }
    logger.debug('Offer amount:', offerAmount);

    // For auction players, validate bid amount
    if (isAuctionPlayer) {
      const bidAmount = Number(offerAmount);
      const minBidRequired = currentBid + 1000;

      if (bidAmount < minBidRequired) {
        showAlertMessage(
          'Bid Too Low',
          `Your bid must be at least ${formatPlayerValue(minBidRequired)} (current bid + £1K).`
        );
        return;
      }

      // Check if auction has ended
      if (player.auctionEndTime < Date.now()) {
        showAlertMessage('Auction Ended', 'This auction has already ended.');
        return;
      }
    }

    try {
      setIsSubmitting(true);

      if (isAuctionPlayer) {
        // Submit bid for auction player
        const bidResponse = await callApi<{ maxBid: number; highestBidder: boolean }>(
          `/transfer/bid`,
          {
            method: 'POST',
            body: JSON.stringify({
              player: player.playerId,
              maxBid: Math.floor(Number(offerAmount)),
              myTeam: manager?.team?.teamId,
            }),
          }
        );

        // Process the response to get maxBid and highestBidder status
        setMaxBid(bidResponse.maxBid);
        setIsHighestBidder(bidResponse.highestBidder);

        // Update the player in cache with new bid information if it's a TransferListPlayer

        const updatedBidHistory = player.bidHistory || [];
        const newBid = {
          teamId: manager?.team?.teamId || '',
          teamName: manager?.team?.teamName || 'Your Team',
          maximumBid: bidResponse.maxBid,
          bidTime: Date.now(),
        };

        updatePlayer({
          playerId: player.playerId,
          bidHistory: [...updatedBidHistory, newBid],
          auctionCurrentPrice: Math.max(player.auctionCurrentPrice, bidResponse.maxBid),
        });

        setIsOfferModalVisible(false);
        showAlertMessage(
          'Success',
          `Bid submitted successfully! ${bidResponse.highestBidder ? 'You are the highest bidder!' : 'You are not the highest bidder.'}`
        );
      } else {
        // Submit transfer offer for team player
        const transferResponse = await callApi<{ message: string; request: ActiveTransfer }>(
          `/transfer/offer`,
          {
            method: 'POST',
            body: JSON.stringify({
              player: player.playerId,
              offer: Number(offerAmount),
              theirTeam: player.teamId,
              myTeam: manager?.team?.teamId,
            }),
          }
        );

        // Create a new transfer object for immediate UI update
        if (onNewTransfer && manager?.team) {
          const newTransfer: ActiveTransfer = {
            id: transferResponse.request.id || `temp-${Date.now()}`, // Use API response ID or temporary ID
            date: Date.now(),
            player: {
              playerId: player.playerId,
              gameworldId: player.gameworldId,
              team: player.teamId || '', // Handle optional teamId
              age: player.age,
              seed: 0, // TransferListPlayer doesn't have seed, use default
              firstName: player.firstName,
              surname: player.surname,
              value: player.value,
              energy: 100, // TransferListPlayer doesn't have energy, use default
              lastMatchPlayed: Date.now(), // TransferListPlayer doesn't have this, use current time
              injuredUntil: null, // TransferListPlayer doesn't have this, use null
              suspendedForGames: 0, // TransferListPlayer doesn't have this, use default
              isTransferListed: true, // Since this is a transfer list player
            },
            buyer: manager.team.teamId,
            seller: {
              teamId: player.teamId || '', // Handle optional teamId
              gameworldId: player.gameworldId,
              league: '', // We don't have this info, will be updated by API refresh
              tier: 0,
              teamName: '', // We don't have this info, will be updated by API refresh
              managerId: null,
              balance: 0,
              played: 0,
              points: 0,
              goalsFor: 0,
              goalsAgainst: 0,
              wins: 0,
              draws: 0,
              losses: 0,
              selectionOrder: [],
            },
            value: Number(offerAmount),
            counterOfferTime: '',
            counterOfferValue: '',
          };
          onNewTransfer(newTransfer);
        }

        setIsOfferModalVisible(false);
        showAlertMessage('Success', 'Transfer offer submitted successfully!');
      }
    } catch (error) {
      logger.error('Error submitting transfer offer/bid:', error);
      setIsReleaseModalVisible(false);
      showAlertMessage(
        'Error',
        `Failed to submit ${isAuctionPlayer ? 'bid' : 'transfer offer'}. Please try again.`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReleasePlayer = async (player: Player, onClose: () => void) => {
    if (!userTeam || !manager) {
      showAlertMessage('Error', 'Unable to release player. Team information not available.');
      return;
    }

    setIsReleasing(true);
    try {
      const response = await callApi<{
        error?: string;
        message: string;
        playerId: string;
        playerName: string;
      }>('/transfer/release-player', {
        method: 'POST',
        body: JSON.stringify({
          playerId: player.playerId,
          teamId: userTeam.teamId,
        }),
      });

      if (response.status !== 200) {
        showAlertMessage('Error', response.error || 'Unable to release player.');
        return;
      }

      // Update the team in cache by removing the player
      const updatedPlayers = userTeam.players.filter((p) => p.playerId !== player.playerId);
      updateTeam({
        players: updatedPlayers,
      });

      setIsReleaseModalVisible(false);
      showAlertMessage(
        'Success',
        response.message || `${player.firstName} ${player.surname} has been released successfully!`
      );
      setAlertCloseAction(goBack);
      onClose();
    } catch (error) {
      logger.error('Error releasing player:', error);
      setIsReleaseModalVisible(false);
      showAlertMessage('Error', 'Failed to release player. Please try again.');
    } finally {
      setIsReleasing(false);
    }
  };

  const prefillOfferAmount = (
    player: TransferListPlayer,
    isAuctionPlayer: boolean,
    currentBid: number
  ) => {
    if (isAuctionPlayer && currentBid) {
      // For auctions, prefill with minimum bid (current bid + 1000)
      setOfferAmount(currentBid + 1000);
    } else {
      // For transfers, prefill with player value
      setOfferAmount(Math.ceil(player.value));
    }
  };

  const state: PlayerActionsState = {
    isOfferModalVisible,
    offerAmount,
    isSubmitting,
    showAlert,
    alertCloseAction,
    alertMessage,
    maxBid,
    isHighestBidder,
    isReleaseModalVisible,
    isReleasing,
  };

  const actions: PlayerActionsActions = {
    setIsOfferModalVisible,
    setOfferAmount,
    setShowAlert,
    setAlertCloseAction,
    setIsReleaseModalVisible,
    handleSubmitOffer,
    handleReleasePlayer,
    prefillOfferAmount,
  };

  return [state, actions];
};
