import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { TransferListPlayer } from '../../models/player';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { BidHistory } from './BidHistory';
import { useAuctionTimer } from './hooks/useAuctionTimer';

import { useDataCache } from '../../context/DataCacheContext';
import { usePlayerUtils } from '../../context/PlayerContext';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferSectionProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  maxBid: number | null;
  isHighestBidder: boolean;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

export const TransferSection: React.FC<TransferSectionProps> = ({
  player,
  playerStatus,
  isPlayerInUserTeam,
  isAuctionPlayer,
  maxBid,
  isHighestBidder,
}) => {
  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';
  const { playerActionsState, playerActions } = usePlayerUtils();
  const { timeRemaining, auctionCompleted } = useAuctionTimer(player);
  const { cache } = useDataCache();

  const handleTransferPress = () => {
    playerActions.prefillOfferAmount(
      player as TransferListPlayer,
      playerStatus.isAuctionPlayer,
      currentBid
    );
    playerActions.setIsOfferModalVisible(true);
  };

  if (isPlayerInUserTeam) {
    return (
      <ActionButton variant="primary" onPress={() => playerActions.setIsReleaseModalVisible(true)}>
        <ActionButtonText>Release Player</ActionButtonText>
      </ActionButton>
    );
  }

  const alreadyMadeOffer = cache.players.myBidsPlayers.some(
    (transfer) => transfer.playerId === player.playerId
  );

  return (
    <>
      {isAuctionPlayer && (
        <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
          <StatusText>
            Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
          </StatusText>
          <StatusText>{timeRemaining}</StatusText>
          {maxBid && (
            <StatusText
              style={{
                color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
                fontFamily: 'NunitoBold',
              }}
            >
              {isHighestBidder
                ? '✓ You are the highest bidder!'
                : '✗ You are not the highest bidder'}
            </StatusText>
          )}
        </StatusContainer>
      )}

      {/* Only show Bid/Offer button if auction is not completed */}
      {!(alreadyMadeOffer && isAuctionPlayer && auctionCompleted) && (
        <ActionButton variant="primary" onPress={handleTransferPress}>
          <ActionButtonText>
            {player.teamId === '' ? 'Bid Now' : 'Submit Transfer Offer'}
          </ActionButtonText>
        </ActionButton>
      )}

      {alreadyMadeOffer && !isAuctionPlayer && !auctionCompleted && (
        <ActionButton
          variant="primary"
          onPress={() => {
            onNegotiate(transfer!);
            onClose();
          }}
        >
          <ActionButtonText>Amend Offer</ActionButtonText>
        </ActionButton>
      )}

      {/* Bid History Section */}
      {isAuctionPlayer && <BidHistory transferListPlayer={player} />}
    </>
  );
};
