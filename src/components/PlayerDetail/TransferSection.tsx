import React, { useEffect, useState } from 'react';
import styled from 'styled-components/native';
import { TransferListPlayer } from '../../models/player';
import { formatPlayerValue } from '../../utils/PlayerUtils';
import { Text } from '../Text';
import { BidHistory } from './BidHistory';
import { useAuctionTimer } from './hooks/useAuctionTimer';

import { usePlayerUtils } from '../../context/PlayerContext';
import { useCachedMyActiveTransfers } from '../../hooks/useCachedData';
import { ActiveTransfer } from '../../hooks/useMyBidsPlayers';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface TransferSectionProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  maxBid: number | null;
  isHighestBidder: boolean;
  onNegotiate: (transfer: ActiveTransfer) => void;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

export const TransferSection: React.FC<TransferSectionProps> = ({
  player,
  playerStatus,
  isPlayerInUserTeam,
  isAuctionPlayer,
  maxBid,
  isHighestBidder,
  onNegotiate,
}) => {
  const [alreadyMadeOffer, setAlreadyMadeOffer] = useState(false);
  const [transfer, setTransfer] = useState<ActiveTransfer | null>(null);
  const { playerActions } = usePlayerUtils();
  const { timeRemaining, auctionCompleted } = useAuctionTimer(player);
  const { data: myBidsData } = useCachedMyActiveTransfers(player.gameworldId);

  useEffect(() => {
    const matchingTransfer = myBidsData.transfers.find(
      (t) => t.player.playerId === player.playerId
    );
    setAlreadyMadeOffer(!!matchingTransfer);
    setTransfer(matchingTransfer || null);
  }, [myBidsData.transfers, player.playerId]);

  const handleTransferPress = () => {
    playerActions.prefillOfferAmount(
      player as TransferListPlayer,
      playerStatus.isAuctionPlayer,
      currentBid
    );
    playerActions.setIsOfferModalVisible(true);
  };

  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';

  if (isPlayerInUserTeam) {
    return (
      <ActionButton variant="primary" onPress={() => playerActions.setIsReleaseModalVisible(true)}>
        <ActionButtonText>Release Player</ActionButtonText>
      </ActionButton>
    );
  }

  return (
    <>
      {isAuctionPlayer && (
        <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
          <StatusText>
            Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
          </StatusText>
          <StatusText>{timeRemaining}</StatusText>
          {maxBid && (
            <StatusText
              style={{
                color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
                fontFamily: 'NunitoBold',
              }}
            >
              {isHighestBidder
                ? '✓ You are the highest bidder!'
                : '✗ You are not the highest bidder'}
            </StatusText>
          )}
        </StatusContainer>
      )}

      {/* Only show Bid/Offer button if auction is not completed and no offer has been made */}
      {!alreadyMadeOffer && (!isAuctionPlayer || !auctionCompleted) && (
        <ActionButton variant="primary" onPress={handleTransferPress}>
          <ActionButtonText>
            {player.teamId === '' ? 'Bid Now' : 'Submit Transfer Offer'}
          </ActionButtonText>
        </ActionButton>
      )}

      {/* Show Amend Offer button only if an offer has already been made, not auction, not completed, and transfer exists */}
      {alreadyMadeOffer && !isAuctionPlayer && !auctionCompleted && transfer && (
        <ActionButton
          variant="primary"
          onPress={() => {
            onNegotiate(transfer);
          }}
        >
          <ActionButtonText>Amend Offer</ActionButtonText>
        </ActionButton>
      )}

      {/* Bid History Section */}
      {isAuctionPlayer && <BidHistory transferListPlayer={player} />}
    </>
  );
};
